"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Textarea } from "@/components/ui/textarea"
import { cn } from "@/lib/utils"
import { 
  Zap, 
  Gamepad2, 
  CheckCircle, 
  AlertCircle, 
  Clock,
  Shield,
  Star,
  ShoppingCart,
  Calculator
} from "lucide-react"

// ## Data Types for Dynamic Product System
interface ProductField {
  label: string
  type: "text" | "email" | "textarea" | "number"
  name: string
  placeholder?: string
  required?: boolean
}

interface SelectorOption {
  label: string
  price: number
  value: string
}

interface ProductSelector {
  label: string
  name: string
  options: SelectorOption[]
  required?: boolean
}

interface GoPlayProductData {
  id: string
  name: string
  image: string
  description?: string
  note?: string
  basePrice?: number
  fields: ProductField[]
  selectors: ProductSelector[]
  category?: string
  inStock?: boolean
  estimatedTime?: string
  features?: string[]
}

interface GoPlayProductComponentProps {
  product: GoPlayProductData
  onSubmit?: (data: any) => void
  className?: string
}

export function GoPlayProductComponent({ 
  product, 
  onSubmit,
  className 
}: GoPlayProductComponentProps) {
  const [formData, setFormData] = useState<Record<string, any>>({})
  const [selectedOptions, setSelectedOptions] = useState<Record<string, string>>({})
  const [totalPrice, setTotalPrice] = useState(product.basePrice || 0)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})

  // Calculate total price based on selected options
  useEffect(() => {
    let calculatedPrice = product.basePrice || 0
    
    product.selectors.forEach(selector => {
      const selectedValue = selectedOptions[selector.name]
      if (selectedValue) {
        const selectedOption = selector.options.find(opt => opt.value === selectedValue)
        if (selectedOption) {
          calculatedPrice += selectedOption.price
        }
      }
    })
    
    setTotalPrice(calculatedPrice)
  }, [selectedOptions, product.basePrice, product.selectors])

  // Handle form field changes
  const handleFieldChange = (fieldName: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [fieldName]: value
    }))
    
    // Clear error when user starts typing
    if (errors[fieldName]) {
      setErrors(prev => ({
        ...prev,
        [fieldName]: ""
      }))
    }
  }

  // Handle selector changes
  const handleSelectorChange = (selectorName: string, value: string) => {
    setSelectedOptions(prev => ({
      ...prev,
      [selectorName]: value
    }))
  }

  // Validate form
  const validateForm = () => {
    const newErrors: Record<string, string> = {}
    
    // Validate required fields
    product.fields.forEach(field => {
      if (field.required && !formData[field.name]) {
        newErrors[field.name] = `${field.label} مطلوب`
      }
    })
    
    // Validate required selectors
    product.selectors.forEach(selector => {
      if (selector.required && !selectedOptions[selector.name]) {
        newErrors[selector.name] = `${selector.label} مطلوب`
      }
    })
    
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // Handle form submission
  const handleSubmit = async () => {
    if (!validateForm()) return
    
    setIsSubmitting(true)
    
    try {
      const submissionData = {
        productId: product.id,
        productName: product.name,
        formData,
        selectedOptions,
        totalPrice,
        timestamp: new Date().toISOString()
      }
      
      // ## TODO: Replace with actual Supabase submission logic
      console.log("Product Order Submission:", submissionData)
      
      if (onSubmit) {
        await onSubmit(submissionData)
      } else {
        // Default fake submission handler
        await new Promise(resolve => setTimeout(resolve, 1000))
        alert("تم إرسال الطلب بنجاح! سيتم التواصل معك قريباً.")
      }
      
    } catch (error) {
      console.error("Submission error:", error)
      alert("حدث خطأ أثناء إرسال الطلب. يرجى المحاولة مرة أخرى.")
    } finally {
      setIsSubmitting(false)
    }
  }

  // Render form field based on type
  const renderField = (field: ProductField) => {
    const value = formData[field.name] || ""
    const hasError = !!errors[field.name]
    const baseClasses = "bg-slate-800/50 border-slate-700/50 text-white placeholder:text-slate-400 focus:border-yellow-400 text-sm sm:text-base h-12"

    switch (field.type) {
      case "textarea":
        return (
          <Textarea
            placeholder={field.placeholder || field.label}
            value={value}
            onChange={(e) => handleFieldChange(field.name, e.target.value)}
            className={cn(
              baseClasses,
              "min-h-[96px] h-24 resize-none",
              hasError && "border-red-500"
            )}
            rows={3}
          />
        )

      case "email":
        return (
          <Input
            type="email"
            placeholder={field.placeholder || field.label}
            value={value}
            onChange={(e) => handleFieldChange(field.name, e.target.value)}
            className={cn(
              baseClasses,
              hasError && "border-red-500"
            )}
          />
        )

      case "number":
        return (
          <Input
            type="number"
            placeholder={field.placeholder || field.label}
            value={value}
            onChange={(e) => handleFieldChange(field.name, e.target.value)}
            className={cn(
              baseClasses,
              hasError && "border-red-500"
            )}
          />
        )

      default: // text
        return (
          <Input
            type="text"
            placeholder={field.placeholder || field.label}
            value={value}
            onChange={(e) => handleFieldChange(field.name, e.target.value)}
            className={cn(
              baseClasses,
              hasError && "border-red-500"
            )}
          />
        )
    }
  }

  return (
    <div className={cn("space-y-4 sm:space-y-6 w-full", className)}>
      {/* Product Header */}
      <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
        <CardContent className="p-4 sm:p-6">
          <div className="flex flex-col lg:flex-row gap-4 sm:gap-6">
            {/* Product Image */}
            <div className="flex-shrink-0 w-full lg:w-auto">
              <div className="w-full lg:w-48 h-48 sm:h-56 lg:h-48 rounded-lg overflow-hidden mx-auto lg:mx-0 max-w-sm lg:max-w-none">
                {product.image ? (
                  <img
                    src={product.image}
                    alt={product.name}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="w-full h-full bg-gradient-to-br from-slate-700 to-slate-800 flex items-center justify-center">
                    <Gamepad2 className="h-12 w-12 sm:h-16 sm:w-16 text-slate-400" />
                  </div>
                )}
              </div>
            </div>
            
            {/* Product Info */}
            <div className="flex-1 space-y-3 sm:space-y-4">
              <div>
                <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold text-white mb-2 text-center lg:text-right">
                  {product.name}
                </h1>

                {product.description && (
                  <p className="text-slate-300 text-sm sm:text-base mb-4 text-center lg:text-right leading-relaxed">
                    {product.description}
                  </p>
                )}
              </div>

              {product.note && (
                <Alert className="mb-4 bg-yellow-400/10 border-yellow-400/20">
                  <AlertCircle className="h-4 w-4 text-yellow-400" />
                  <AlertDescription className="text-yellow-200 text-sm">
                    {product.note}
                  </AlertDescription>
                </Alert>
              )}

              <div className="flex flex-wrap gap-2 mb-4 justify-center lg:justify-start">
                {product.category && (
                  <Badge variant="outline" className="border-slate-600 text-slate-300 text-xs sm:text-sm">
                    {product.category}
                  </Badge>
                )}

                {product.estimatedTime && (
                  <Badge variant="outline" className="border-slate-600 text-slate-300 text-xs sm:text-sm">
                    <Clock className="h-3 w-3 mr-1" />
                    {product.estimatedTime}
                  </Badge>
                )}

                {product.inStock !== false && (
                  <Badge className="bg-green-500/20 text-green-400 border-green-500/30 text-xs sm:text-sm">
                    <CheckCircle className="h-3 w-3 mr-1" />
                    متوفر
                  </Badge>
                )}
              </div>
              
              {product.features && product.features.length > 0 && (
                <div className="text-center lg:text-right">
                  <h3 className="text-sm sm:text-base font-semibold text-slate-300 mb-2">المميزات:</h3>
                  <ul className="text-xs sm:text-sm text-slate-400 space-y-1">
                    {product.features.map((feature, index) => (
                      <li key={index} className="flex items-center gap-2 justify-center lg:justify-start">
                        <CheckCircle className="h-3 w-3 text-green-400 flex-shrink-0" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Order Form */}
      <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
        <CardHeader className="pb-4">
          <CardTitle className="text-white flex items-center gap-2 text-lg sm:text-xl">
            <ShoppingCart className="h-5 w-5" />
            تفاصيل الطلب
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4 sm:space-y-6 p-4 sm:p-6">
          {/* Selectors */}
          {product.selectors.map((selector) => (
            <div key={selector.name} className="space-y-2">
              <Label className="text-white font-medium text-sm sm:text-base">
                {selector.label}
                {selector.required && <span className="text-red-400 mr-1">*</span>}
              </Label>
              <Select
                value={selectedOptions[selector.name] || ""}
                onValueChange={(value) => handleSelectorChange(selector.name, value)}
              >
                <SelectTrigger className={cn(
                  "bg-slate-800/50 border-slate-700/50 text-white focus:border-yellow-400 h-12 text-sm sm:text-base",
                  errors[selector.name] && "border-red-500"
                )}>
                  <SelectValue placeholder={`اختر ${selector.label}`} />
                </SelectTrigger>
                <SelectContent className="bg-slate-800 border-slate-700 max-h-60">
                  {selector.options.map((option) => (
                    <SelectItem
                      key={option.value}
                      value={option.value}
                      className="text-white hover:bg-slate-700 focus:bg-slate-700 text-sm sm:text-base"
                    >
                      <div className="flex justify-between items-center w-full">
                        <span className="flex-1">{option.label}</span>
                        <span className="text-yellow-400 font-medium mr-2 text-xs sm:text-sm">
                          {option.price > 0 ? `+${option.price}` : option.price} ج.س
                        </span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors[selector.name] && (
                <p className="text-red-400 text-xs sm:text-sm">{errors[selector.name]}</p>
              )}
            </div>
          ))}

          {/* Form Fields */}
          {product.fields.map((field) => (
            <div key={field.name} className="space-y-2">
              <Label className="text-white font-medium text-sm sm:text-base">
                {field.label}
                {field.required && <span className="text-red-400 mr-1">*</span>}
              </Label>
              {renderField(field)}
              {errors[field.name] && (
                <p className="text-red-400 text-xs sm:text-sm">{errors[field.name]}</p>
              )}
            </div>
          ))}

          {/* Price Summary */}
          <div className="border-t border-slate-700/50 pt-4 sm:pt-6">
            <div className="bg-slate-900/50 rounded-lg p-3 sm:p-4 space-y-3">
              <div className="flex items-center gap-2 text-white font-medium text-sm sm:text-base">
                <Calculator className="h-4 w-4" />
                ملخص السعر
              </div>

              {product.basePrice && product.basePrice > 0 && (
                <div className="flex justify-between text-slate-300 text-sm sm:text-base">
                  <span>السعر الأساسي</span>
                  <span>{product.basePrice} ج.س</span>
                </div>
              )}

              {product.selectors.map((selector) => {
                const selectedValue = selectedOptions[selector.name]
                const selectedOption = selectedValue
                  ? selector.options.find(opt => opt.value === selectedValue)
                  : null

                if (!selectedOption || selectedOption.price === 0) return null

                return (
                  <div key={selector.name} className="flex justify-between text-slate-300 text-sm sm:text-base">
                    <span className="flex-1 truncate">{selectedOption.label}</span>
                    <span className="text-yellow-400 font-medium">
                      +{selectedOption.price} ج.س
                    </span>
                  </div>
                )
              })}

              <div className="border-t border-slate-700/50 pt-3 flex justify-between text-base sm:text-lg font-bold">
                <span className="text-white">المجموع الكلي</span>
                <span className="text-yellow-400">{totalPrice} ج.س</span>
              </div>
            </div>
          </div>

          {/* Submit Button */}
          <Button
            onClick={handleSubmit}
            disabled={isSubmitting || product.inStock === false}
            className="w-full bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 text-slate-900 font-bold py-3 sm:py-4 text-base sm:text-lg disabled:opacity-50 disabled:cursor-not-allowed min-h-[48px] sm:min-h-[56px]"
          >
            {isSubmitting ? (
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 border-2 border-slate-900/30 border-t-slate-900 rounded-full animate-spin" />
                <span className="text-sm sm:text-base">جاري المعالجة...</span>
              </div>
            ) : product.inStock === false ? (
              <span className="text-sm sm:text-base">غير متوفر حالياً</span>
            ) : (
              <div className="flex items-center gap-2">
                <Zap className="h-4 w-4 sm:h-5 sm:w-5" />
                <span className="text-sm sm:text-base">اطلب الآن - {totalPrice} ج.س</span>
              </div>
            )}
          </Button>
        </CardContent>
      </Card>
    </div>
  )
}

// Export the component and types for use in other files
export type { GoPlayProductData, ProductField, ProductSelector, SelectorOption }
