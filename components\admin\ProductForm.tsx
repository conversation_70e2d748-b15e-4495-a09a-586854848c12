"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, Card<PERSON>itle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { 
  Package, 
  Save, 
  X, 
  Image, 
  Clock, 
  DollarSign,
  AlertCircle,
  Settings,
  Sparkles
} from "lucide-react"

// Import our custom components
import { CustomFieldEditor, ProductField } from "./CustomFieldEditor"
import { SelectMenuEditor, ProductSelector } from "./SelectMenuEditor"

// ## Complete Product Interface for Supabase Integration
export interface DynamicProduct {
  id?: string
  name: string
  image: string
  description: string
  note: string
  estimatedTime: string
  price: number
  category: string
  active: boolean
  inStock: boolean
  fields: ProductField[]
  selectors: ProductSelector[]
  // ## TODO: Add Supabase metadata fields
  created_at?: string
  updated_at?: string
  created_by?: string
}

interface ProductFormProps {
  product?: DynamicProduct
  onSave: (product: DynamicProduct) => void
  onCancel: () => void
  categories: Array<{ id: string; name: string }>
  isEditing?: boolean
}

export function ProductForm({ 
  product, 
  onSave, 
  onCancel, 
  categories, 
  isEditing = false 
}: ProductFormProps) {
  
  // ## Form State Management
  const [formData, setFormData] = useState<DynamicProduct>({
    name: product?.name || "",
    image: product?.image || "",
    description: product?.description || "",
    note: product?.note || "",
    estimatedTime: product?.estimatedTime || "",
    price: product?.price || 0,
    category: product?.category || "",
    active: product?.active ?? true,
    inStock: product?.inStock ?? true,
    fields: product?.fields || [],
    selectors: product?.selectors || []
  })

  const [errors, setErrors] = useState<Record<string, string>>({})
  const [isSaving, setIsSaving] = useState(false)

  // ## Form Validation
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    if (!formData.name.trim()) {
      newErrors.name = "اسم المنتج مطلوب"
    }

    if (!formData.category) {
      newErrors.category = "الفئة مطلوبة"
    }

    if (!formData.description.trim()) {
      newErrors.description = "وصف المنتج مطلوب"
    }

    if (!formData.estimatedTime.trim()) {
      newErrors.estimatedTime = "الوقت المقدر مطلوب"
    }

    if (formData.price < 0) {
      newErrors.price = "السعر يجب أن يكون أكبر من أو يساوي صفر"
    }

    // ## Validate custom fields
    formData.fields.forEach((field, index) => {
      if (!field.label.trim()) {
        newErrors[`field_${index}_label`] = "تسمية الحقل مطلوبة"
      }
      if (!field.name.trim()) {
        newErrors[`field_${index}_name`] = "الاسم الداخلي للحقل مطلوب"
      }
    })

    // ## Validate selectors
    formData.selectors.forEach((selector, index) => {
      if (!selector.label.trim()) {
        newErrors[`selector_${index}_label`] = "تسمية القائمة مطلوبة"
      }
      if (!selector.name.trim()) {
        newErrors[`selector_${index}_name`] = "الاسم الداخلي للقائمة مطلوب"
      }
      if (selector.options.length === 0) {
        newErrors[`selector_${index}_options`] = "يجب إضافة خيار واحد على الأقل"
      }
    })

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // ## Handle Form Submission
  const handleSubmit = async () => {
    if (!validateForm()) return

    setIsSaving(true)
    
    try {
      const productData: DynamicProduct = {
        ...formData,
        id: product?.id || Date.now().toString(),
        // ## TODO: Add Supabase timestamp and user fields
        created_at: product?.created_at || new Date().toISOString(),
        updated_at: new Date().toISOString()
      }

      // ## TODO: Save to Supabase database
      // const { data, error } = await supabase
      //   .from('products')
      //   .upsert(productData)
      
      onSave(productData)
    } catch (error) {
      console.error('Error saving product:', error)
      // ## TODO: Handle Supabase errors
    } finally {
      setIsSaving(false)
    }
  }

  // ## Update form field
  const updateField = (field: keyof DynamicProduct, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: "" }))
    }
  }

  return (
    <div className="space-y-3 sm:space-y-6 w-full max-w-full">
      {/* Header */}
      <Card className="bg-slate-800/50 border-slate-700/50">
        <CardHeader className="p-3 sm:p-6">
          <CardTitle className="text-white flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-3 text-base sm:text-xl">
            <div className="flex items-center gap-2">
              <Package className="h-4 w-4 sm:h-6 sm:w-6 text-yellow-400" />
              {isEditing ? "تعديل المنتج" : "إضافة منتج جديد"}
            </div>
            <Badge variant="outline" className="text-purple-400 border-purple-400 text-xs">
              <Sparkles className="h-3 w-3 mr-1" />
              نظام ديناميكي
            </Badge>
          </CardTitle>
        </CardHeader>
      </Card>

      {/* Basic Product Information */}
      <Card className="bg-slate-800/50 border-slate-700/50">
        <CardHeader className="p-3 sm:p-6 pb-2 sm:pb-6">
          <CardTitle className="text-white flex items-center gap-2 text-base sm:text-lg">
            <Settings className="h-4 w-4 sm:h-5 sm:w-5 text-blue-400" />
            المعلومات الأساسية
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-2 sm:space-y-4 p-2 sm:p-6 pt-0">
          <div className="grid grid-cols-1 gap-3 sm:gap-4">
            {/* Product Name */}
            <div>
              <Label className="text-slate-300">اسم المنتج *</Label>
              <Input
                value={formData.name}
                onChange={(e) => updateField('name', e.target.value)}
                className="bg-slate-700/50 border-slate-600 text-white"
                placeholder="مثال: شحن 100 جوهرة"
              />
              {errors.name && (
                <p className="text-red-400 text-sm mt-1 flex items-center gap-1">
                  <AlertCircle className="h-3 w-3" />
                  {errors.name}
                </p>
              )}
            </div>

            {/* Category */}
            <div>
              <Label className="text-slate-300">الفئة *</Label>
              <Select value={formData.category} onValueChange={(value) => updateField('category', value)}>
                <SelectTrigger className="bg-slate-700/50 border-slate-600 text-white">
                  <SelectValue placeholder="اختر الفئة" />
                </SelectTrigger>
                <SelectContent className="bg-slate-800 border-slate-700">
                  {categories.map((category) => (
                    <SelectItem key={category.id} value={category.id} className="text-white hover:bg-slate-700">
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.category && (
                <p className="text-red-400 text-sm mt-1 flex items-center gap-1">
                  <AlertCircle className="h-3 w-3" />
                  {errors.category}
                </p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {/* Base Price */}
            <div>
              <Label className="text-slate-300 flex items-center gap-2">
                <DollarSign className="h-4 w-4" />
                السعر الأساسي (ج.س)
              </Label>
              <Input
                type="number"
                value={formData.price}
                onChange={(e) => updateField('price', Number(e.target.value))}
                className="bg-slate-700/50 border-slate-600 text-white"
                placeholder="0"
                min="0"
              />
              <p className="text-slate-400 text-xs mt-1">
                سيتم تجاهل هذا السعر إذا تم استخدام قوائم الاختيار مع أسعار
              </p>
              {errors.price && (
                <p className="text-red-400 text-sm mt-1 flex items-center gap-1">
                  <AlertCircle className="h-3 w-3" />
                  {errors.price}
                </p>
              )}
            </div>

            {/* Estimated Time */}
            <div>
              <Label className="text-slate-300 flex items-center gap-2">
                <Clock className="h-4 w-4" />
                الوقت المقدر *
              </Label>
              <Input
                value={formData.estimatedTime}
                onChange={(e) => updateField('estimatedTime', e.target.value)}
                className="bg-slate-700/50 border-slate-600 text-white"
                placeholder="مثال: 5-15 دقيقة"
              />
              {errors.estimatedTime && (
                <p className="text-red-400 text-sm mt-1 flex items-center gap-1">
                  <AlertCircle className="h-3 w-3" />
                  {errors.estimatedTime}
                </p>
              )}
            </div>
          </div>

          {/* Description */}
          <div>
            <Label className="text-slate-300">وصف المنتج *</Label>
            <Textarea
              value={formData.description}
              onChange={(e) => updateField('description', e.target.value)}
              className="bg-slate-700/50 border-slate-600 text-white min-h-[100px]"
              placeholder="وصف مختصر للمنتج..."
            />
            {errors.description && (
              <p className="text-red-400 text-sm mt-1 flex items-center gap-1">
                <AlertCircle className="h-3 w-3" />
                {errors.description}
              </p>
            )}
          </div>

          {/* Important Note */}
          <div>
            <Label className="text-slate-300">ملاحظة مهمة</Label>
            <Textarea
              value={formData.note}
              onChange={(e) => updateField('note', e.target.value)}
              className="bg-slate-700/50 border-slate-600 text-white"
              placeholder="معلومات مهمة للعميل..."
            />
          </div>

          {/* Image URL */}
          <div>
            <Label className="text-slate-300 flex items-center gap-2">
              <Image className="h-4 w-4" />
              رابط الصورة
            </Label>
            <Input
              value={formData.image}
              onChange={(e) => updateField('image', e.target.value)}
              className="bg-slate-700/50 border-slate-600 text-white"
              placeholder="https://example.com/image.jpg"
            />
          </div>

          {/* Status Toggles */}
          <div className="flex gap-6">
            <div className="flex items-center gap-2">
              <input
                type="checkbox"
                id="active"
                checked={formData.active}
                onChange={(e) => updateField('active', e.target.checked)}
                className="w-4 h-4"
              />
              <Label htmlFor="active" className="text-slate-300">
                منتج نشط
              </Label>
            </div>
            
            <div className="flex items-center gap-2">
              <input
                type="checkbox"
                id="inStock"
                checked={formData.inStock}
                onChange={(e) => updateField('inStock', e.target.checked)}
                className="w-4 h-4"
              />
              <Label htmlFor="inStock" className="text-slate-300">
                متوفر في المخزون
              </Label>
            </div>
          </div>
        </CardContent>
      </Card>

      <Separator className="bg-slate-600" />

      {/* Custom Fields Section */}
      <Card className="bg-slate-800/50 border-slate-700/50">
        <CardContent className="p-2 sm:p-6">
          <CustomFieldEditor
            fields={formData.fields}
            onChange={(fields) => updateField('fields', fields)}
          />
        </CardContent>
      </Card>

      <Separator className="bg-slate-600" />

      {/* Select Menus Section */}
      <Card className="bg-slate-800/50 border-slate-700/50">
        <CardContent className="p-2 sm:p-6">
          <SelectMenuEditor
            selectors={formData.selectors}
            onChange={(selectors) => updateField('selectors', selectors)}
          />
        </CardContent>
      </Card>

      {/* Action Buttons */}
      <Card className="bg-slate-800/50 border-slate-700/50">
        <CardContent className="p-3 sm:p-6">
          <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 sm:justify-end">
            <Button
              variant="outline"
              onClick={onCancel}
              className="border-slate-600 text-slate-300 hover:border-red-400 hover:text-red-400 w-full sm:w-auto order-2 sm:order-1"
            >
              <X className="h-4 w-4 mr-2" />
              إلغاء
            </Button>

            <Button
              onClick={handleSubmit}
              disabled={isSaving}
              className="bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white w-full sm:w-auto order-1 sm:order-2"
            >
              <Save className="h-4 w-4 mr-2" />
              {isSaving ? "جاري الحفظ..." : isEditing ? "تحديث المنتج" : "حفظ المنتج"}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
