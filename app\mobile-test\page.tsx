"use client"

import { useState, useEffect } from "react"
import { GoPlayProductComponent } from "@/components/products/GoPlayProductComponent"
import { AppHeader } from "@/components/layout/AppHeader"
import { SideMenu } from "@/components/layout/SideMenu"
import { MobileNavigation } from "@/components/layout/MobileNavigation"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { 
  Smartphone, 
  CheckCircle, 
  AlertTriangle,
  Monitor,
  Tablet,
  Info
} from "lucide-react"

// Mobile test product with comprehensive form fields
const mobileTestProduct = {
  id: "mobile-test-product",
  name: "اختبار الهاتف المحمول",
  image: "/test/mobile.jpg",
  description: "منتج اختبار شامل للتأكد من عمل جميع المكونات على الهاتف المحمول بشكل مثالي",
  note: "هذا منتج اختبار فقط - لا يتم معالجة الطلبات الفعلية",
  basePrice: 10,
  category: "اختبار",
  inStock: true,
  estimatedTime: "فوري",
  features: [
    "اختبار شامل للهاتف المحمول",
    "جميع أنواع الحقول",
    "تصميم متجاوب",
    "سهولة الاستخدام"
  ],
  fields: [
    {
      label: "النص العادي",
      type: "text" as const,
      name: "normalText",
      placeholder: "أدخل نص عادي هنا",
      required: true
    },
    {
      label: "البريد الإلكتروني",
      type: "email" as const,
      name: "email",
      placeholder: "<EMAIL>",
      required: true
    },
    {
      label: "رقم",
      type: "number" as const,
      name: "number",
      placeholder: "أدخل رقم",
      required: false
    },
    {
      label: "نص طويل",
      type: "textarea" as const,
      name: "longText",
      placeholder: "أدخل نص طويل أو ملاحظات إضافية هنا...",
      required: false
    }
  ],
  selectors: [
    {
      label: "خيار بسيط",
      name: "simpleOption",
      required: true,
      options: [
        { label: "خيار أول", value: "option1", price: 5 },
        { label: "خيار ثاني", value: "option2", price: 10 },
        { label: "خيار ثالث", value: "option3", price: 15 }
      ]
    },
    {
      label: "خيارات متعددة الأسعار",
      name: "multiPriceOption",
      required: true,
      options: [
        { label: "مجاني", value: "free", price: 0 },
        { label: "أساسي - 20 ج.س", value: "basic", price: 20 },
        { label: "متقدم - 50 ج.س", value: "advanced", price: 50 },
        { label: "احترافي - 100 ج.س", value: "professional", price: 100 },
        { label: "مميز - 200 ج.س", value: "premium", price: 200 }
      ]
    }
  ]
}

export default function MobileTestPage() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [activeTab, setActiveTab] = useState("home")
  const [deviceInfo, setDeviceInfo] = useState<{
    width: number
    height: number
    isMobile: boolean
    isTablet: boolean
    userAgent: string
  } | null>(null)
  const [testResults, setTestResults] = useState<string[]>([])

  useEffect(() => {
    const updateDeviceInfo = () => {
      const width = window.innerWidth
      const height = window.innerHeight
      const isMobile = width < 768
      const isTablet = width >= 768 && width < 1024
      
      setDeviceInfo({
        width,
        height,
        isMobile,
        isTablet,
        userAgent: navigator.userAgent
      })
    }

    updateDeviceInfo()
    window.addEventListener('resize', updateDeviceInfo)
    return () => window.removeEventListener('resize', updateDeviceInfo)
  }, [])

  const handleTabChange = (tab: string) => {
    setActiveTab(tab)
  }

  const handleMobileTestSubmit = async (data: any) => {
    console.log("Mobile test submission:", data)
    
    const timestamp = new Date().toLocaleString('ar-SA')
    const result = `✅ ${timestamp} - اختبار ناجح على ${deviceInfo?.isMobile ? 'الهاتف المحمول' : deviceInfo?.isTablet ? 'التابلت' : 'الكمبيوتر'} (${deviceInfo?.width}x${deviceInfo?.height})`
    
    setTestResults(prev => [result, ...prev.slice(0, 9)]) // Keep last 10 results
    
    // Simulate processing
    await new Promise(resolve => setTimeout(resolve, 1500))
    alert(`تم اختبار المكون بنجاح على ${deviceInfo?.isMobile ? 'الهاتف المحمول' : 'الجهاز'}! ✅`)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
      <AppHeader onMenuOpen={() => setIsMenuOpen(true)} />
      <SideMenu isOpen={isMenuOpen} onClose={() => setIsMenuOpen(false)} />

      {/* Main Content */}
      <main className="relative z-10 p-3 sm:p-4 lg:p-8 space-y-4 sm:space-y-6 pb-32 pt-32 lg:pt-36 max-w-6xl mx-auto">
        
        {/* Device Info */}
        {deviceInfo && (
          <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
            <CardHeader className="pb-3">
              <CardTitle className="text-white flex items-center gap-2 text-lg sm:text-xl">
                {deviceInfo.isMobile ? (
                  <Smartphone className="h-5 w-5 text-blue-400" />
                ) : deviceInfo.isTablet ? (
                  <Tablet className="h-5 w-5 text-green-400" />
                ) : (
                  <Monitor className="h-5 w-5 text-purple-400" />
                )}
                معلومات الجهاز
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div className="text-slate-300">
                  <span className="font-medium">النوع:</span> {deviceInfo.isMobile ? 'هاتف محمول' : deviceInfo.isTablet ? 'تابلت' : 'كمبيوتر'}
                </div>
                <div className="text-slate-300">
                  <span className="font-medium">الحجم:</span> {deviceInfo.width}×{deviceInfo.height}
                </div>
              </div>
              
              {deviceInfo.isMobile && (
                <Alert className="bg-blue-500/10 border-blue-500/20">
                  <Smartphone className="h-4 w-4 text-blue-400" />
                  <AlertDescription className="text-blue-200 text-sm">
                    أنت تستخدم هاتف محمول - سيتم اختبار التصميم المحسن للهاتف
                  </AlertDescription>
                </Alert>
              )}
            </CardContent>
          </Card>
        )}

        {/* Mobile Test Instructions */}
        <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="text-white flex items-center gap-2 text-lg sm:text-xl">
              <Info className="h-5 w-5 text-yellow-400" />
              تعليمات الاختبار
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 text-sm sm:text-base text-slate-300">
              <div className="flex items-start gap-2">
                <CheckCircle className="h-4 w-4 text-green-400 mt-0.5 flex-shrink-0" />
                <span>املأ جميع الحقول المطلوبة</span>
              </div>
              <div className="flex items-start gap-2">
                <CheckCircle className="h-4 w-4 text-green-400 mt-0.5 flex-shrink-0" />
                <span>اختبر القوائم المنسدلة والخيارات</span>
              </div>
              <div className="flex items-start gap-2">
                <CheckCircle className="h-4 w-4 text-green-400 mt-0.5 flex-shrink-0" />
                <span>تأكد من سهولة النقر على الأزرار</span>
              </div>
              <div className="flex items-start gap-2">
                <CheckCircle className="h-4 w-4 text-green-400 mt-0.5 flex-shrink-0" />
                <span>راقب حساب السعر التلقائي</span>
              </div>
              <div className="flex items-start gap-2">
                <CheckCircle className="h-4 w-4 text-green-400 mt-0.5 flex-shrink-0" />
                <span>اضغط "اطلب الآن" لإتمام الاختبار</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Mobile Test Component */}
        <div className="space-y-4">
          <h2 className="text-lg sm:text-xl font-bold text-white text-center">
            اختبار شامل للهاتف المحمول
          </h2>
          
          <GoPlayProductComponent
            product={mobileTestProduct}
            onSubmit={handleMobileTestSubmit}
            className="max-w-4xl mx-auto"
          />
        </div>

        {/* Test Results */}
        {testResults.length > 0 && (
          <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2 text-lg">
                <CheckCircle className="h-5 w-5 text-green-400" />
                سجل الاختبارات ({testResults.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 max-h-60 overflow-y-auto">
                {testResults.map((result, index) => (
                  <div key={index} className="bg-slate-900/50 rounded-lg p-2 sm:p-3">
                    <div className="text-xs sm:text-sm text-slate-300 break-words">
                      {result}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Mobile Optimization Status */}
        <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="text-white flex items-center gap-2 text-lg">
              <CheckCircle className="h-5 w-5 text-green-400" />
              حالة التحسين للهاتف المحمول
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-400" />
                <span className="text-sm text-slate-300">تصميم متجاوب</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-400" />
                <span className="text-sm text-slate-300">أزرار بحجم مناسب للمس</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-400" />
                <span className="text-sm text-slate-300">نصوص قابلة للقراءة</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-400" />
                <span className="text-sm text-slate-300">تخطيط محسن للهاتف</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-400" />
                <span className="text-sm text-slate-300">حقول نماذج محسنة</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-400" />
                <span className="text-sm text-slate-300">تفاعل سلس</span>
              </div>
            </div>
          </CardContent>
        </Card>

      </main>

      <MobileNavigation activeTab={activeTab} onTabChange={handleTabChange} />
    </div>
  )
}
