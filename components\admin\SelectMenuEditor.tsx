"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { 
  Plus, 
  Trash2, 
  GripVertical, 
  ChevronDown, 
  Edit3, 
  Check, 
  X,
  DollarSign,
  List
} from "lucide-react"

// ## Interface for Select Menus - will be used with Supabase
export interface SelectorOption {
  id: string
  label: string
  price: number
  value: string
}

export interface ProductSelector {
  id: string
  label: string
  name: string
  options: SelectorOption[]
  required: boolean
}

interface SelectMenuEditorProps {
  selectors: ProductSelector[]
  onChange: (selectors: ProductSelector[]) => void
  className?: string
}

export function SelectMenuEditor({ selectors, onChange, className = "" }: SelectMenuEditorProps) {
  const [editingSelector, setEditingSelector] = useState<string | null>(null)
  const [editingOption, setEditingOption] = useState<string | null>(null)
  const [newSelector, setNewSelector] = useState<Partial<ProductSelector>>({
    label: "",
    name: "",
    required: false,
    options: []
  })

  // ## Generate internal name from label
  const generateInternalName = (label: string): string => {
    return label
      .toLowerCase()
      .replace(/[^a-zA-Z0-9\u0600-\u06FF]/g, '_')
      .replace(/_+/g, '_')
      .replace(/^_|_$/g, '')
  }

  // ## Add new selector
  const addSelector = () => {
    if (!newSelector.label?.trim()) return

    const selector: ProductSelector = {
      id: Date.now().toString(),
      label: newSelector.label.trim(),
      name: newSelector.name?.trim() || generateInternalName(newSelector.label.trim()),
      required: newSelector.required || false,
      options: []
    }

    // ## TODO: Add selector to Supabase database
    onChange([...selectors, selector])
    
    // Reset form
    setNewSelector({
      label: "",
      name: "",
      required: false,
      options: []
    })
  }

  // ## Update existing selector
  const updateSelector = (id: string, updates: Partial<ProductSelector>) => {
    const updatedSelectors = selectors.map(selector => 
      selector.id === id ? { ...selector, ...updates } : selector
    )
    
    // ## TODO: Update selector in Supabase database
    onChange(updatedSelectors)
    setEditingSelector(null)
  }

  // ## Remove selector
  const removeSelector = (id: string) => {
    const updatedSelectors = selectors.filter(selector => selector.id !== id)
    
    // ## TODO: Remove selector from Supabase database
    onChange(updatedSelectors)
  }

  // ## Move selector up/down
  const moveSelector = (id: string, direction: 'up' | 'down') => {
    const currentIndex = selectors.findIndex(selector => selector.id === id)
    if (currentIndex === -1) return

    const newIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1
    if (newIndex < 0 || newIndex >= selectors.length) return

    const newSelectors = [...selectors]
    const [movedSelector] = newSelectors.splice(currentIndex, 1)
    newSelectors.splice(newIndex, 0, movedSelector)

    // ## TODO: Update selector order in Supabase database
    onChange(newSelectors)
  }

  // ## Add option to selector
  const addOption = (selectorId: string, option: Omit<SelectorOption, 'id'>) => {
    const newOption: SelectorOption = {
      id: Date.now().toString(),
      ...option
    }

    const updatedSelectors = selectors.map(selector => 
      selector.id === selectorId 
        ? { ...selector, options: [...selector.options, newOption] }
        : selector
    )

    // ## TODO: Add option to Supabase database
    onChange(updatedSelectors)
  }

  // ## Update option
  const updateOption = (selectorId: string, optionId: string, updates: Partial<SelectorOption>) => {
    const updatedSelectors = selectors.map(selector => 
      selector.id === selectorId 
        ? {
            ...selector,
            options: selector.options.map(option =>
              option.id === optionId ? { ...option, ...updates } : option
            )
          }
        : selector
    )

    // ## TODO: Update option in Supabase database
    onChange(updatedSelectors)
    setEditingOption(null)
  }

  // ## Remove option
  const removeOption = (selectorId: string, optionId: string) => {
    const updatedSelectors = selectors.map(selector => 
      selector.id === selectorId 
        ? { ...selector, options: selector.options.filter(option => option.id !== optionId) }
        : selector
    )

    // ## TODO: Remove option from Supabase database
    onChange(updatedSelectors)
  }

  return (
    <div className={`space-y-2 sm:space-y-4 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <h3 className="text-base sm:text-lg font-semibold text-white flex items-center gap-2">
          <List className="h-4 w-4 sm:h-5 sm:w-5 text-purple-400" />
          قوائم الاختيار ({selectors.length})
        </h3>
      </div>

      {/* Existing Selectors */}
      {selectors.length > 0 && (
        <div className="space-y-2 sm:space-y-4">
          {selectors.map((selector, index) => (
            <Card key={selector.id} className="bg-slate-700/30 border-slate-600/50">
              <CardContent className="p-2 sm:p-4">
                {editingSelector === selector.id ? (
                  // Edit Selector Mode
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                      <div>
                        <Label className="text-slate-300 text-sm">تسمية القائمة</Label>
                        <Input
                          value={selector.label}
                          onChange={(e) => updateSelector(selector.id, { label: e.target.value })}
                          className="bg-slate-800/50 border-slate-600 text-white text-sm"
                          placeholder="مثال: اختر الباقة"
                        />
                      </div>
                      <div>
                        <Label className="text-slate-300 text-sm">الاسم الداخلي</Label>
                        <Input
                          value={selector.name}
                          onChange={(e) => updateSelector(selector.id, { name: e.target.value })}
                          className="bg-slate-800/50 border-slate-600 text-white text-sm"
                          placeholder="package_type"
                        />
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <input
                          type="checkbox"
                          id={`required-selector-${selector.id}`}
                          checked={selector.required}
                          onChange={(e) => updateSelector(selector.id, { required: e.target.checked })}
                          className="w-4 h-4"
                        />
                        <Label htmlFor={`required-selector-${selector.id}`} className="text-slate-300 text-sm">
                          اختيار مطلوب
                        </Label>
                      </div>
                      
                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          onClick={() => setEditingSelector(null)}
                          className="bg-green-600 hover:bg-green-700 text-white"
                        >
                          <Check className="h-3 w-3" />
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => setEditingSelector(null)}
                          className="border-slate-600 text-slate-300"
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ) : (
                  // View Selector Mode
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="flex flex-col gap-1">
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => moveSelector(selector.id, 'up')}
                            disabled={index === 0}
                            className="h-4 w-6 p-0 text-slate-400 hover:text-white disabled:opacity-30"
                          >
                            ▲
                          </Button>
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => moveSelector(selector.id, 'down')}
                            disabled={index === selectors.length - 1}
                            className="h-4 w-6 p-0 text-slate-400 hover:text-white disabled:opacity-30"
                          >
                            ▼
                          </Button>
                        </div>
                        
                        <GripVertical className="h-4 w-4 text-slate-400" />
                        
                        <div>
                          <div className="flex items-center gap-2">
                            <ChevronDown className="h-4 w-4 text-purple-400" />
                            <span className="text-white font-medium">{selector.label}</span>
                            {selector.required && (
                              <Badge variant="destructive" className="text-xs">مطلوب</Badge>
                            )}
                          </div>
                          <div className="text-slate-400 text-sm">
                            {selector.name} • {selector.options.length} خيار
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => setEditingSelector(selector.id)}
                          className="border-slate-600 text-slate-300 hover:border-yellow-400 hover:text-yellow-400"
                        >
                          <Edit3 className="h-3 w-3" />
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => removeSelector(selector.id)}
                          className="border-slate-600 text-slate-300 hover:border-red-400 hover:text-red-400"
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>

                    {/* Options */}
                    <div className="ml-8 space-y-2">
                      {selector.options.map((option) => (
                        <div key={option.id} className="bg-slate-800/30 rounded-lg p-3">
                          {editingOption === option.id ? (
                            // Edit Option Mode
                            <div className="grid grid-cols-1 sm:grid-cols-3 gap-2">
                              <Input
                                value={option.label}
                                onChange={(e) => updateOption(selector.id, option.id, { label: e.target.value })}
                                className="bg-slate-900/50 border-slate-600 text-white text-sm"
                                placeholder="تسمية الخيار"
                              />
                              <Input
                                type="number"
                                value={option.price}
                                onChange={(e) => updateOption(selector.id, option.id, { price: Number(e.target.value) })}
                                className="bg-slate-900/50 border-slate-600 text-white text-sm"
                                placeholder="السعر"
                              />
                              <div className="flex gap-1">
                                <Button
                                  size="sm"
                                  onClick={() => setEditingOption(null)}
                                  className="bg-green-600 hover:bg-green-700 text-white flex-1"
                                >
                                  <Check className="h-3 w-3" />
                                </Button>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => setEditingOption(null)}
                                  className="border-slate-600 text-slate-300"
                                >
                                  <X className="h-3 w-3" />
                                </Button>
                              </div>
                            </div>
                          ) : (
                            // View Option Mode
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-2">
                                <span className="text-slate-300">{option.label}</span>
                                <div className="flex items-center gap-1 text-yellow-400">
                                  <DollarSign className="h-3 w-3" />
                                  <span className="font-medium">{option.price} ج.س</span>
                                </div>
                              </div>
                              <div className="flex gap-1">
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  onClick={() => setEditingOption(option.id)}
                                  className="text-slate-400 hover:text-yellow-400 h-6 w-6 p-0"
                                >
                                  <Edit3 className="h-3 w-3" />
                                </Button>
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  onClick={() => removeOption(selector.id, option.id)}
                                  className="text-slate-400 hover:text-red-400 h-6 w-6 p-0"
                                >
                                  <Trash2 className="h-3 w-3" />
                                </Button>
                              </div>
                            </div>
                          )}
                        </div>
                      ))}

                      {/* Add Option */}
                      <OptionAdder 
                        onAdd={(option) => addOption(selector.id, option)}
                      />
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Add New Selector */}
      <Card className="bg-slate-800/30 border-slate-600/50 border-dashed">
        <CardHeader className="pb-3">
          <CardTitle className="text-white text-base flex items-center gap-2">
            <Plus className="h-4 w-4 text-purple-400" />
            إضافة قائمة اختيار جديدة
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
            <div>
              <Label className="text-slate-300 text-sm">تسمية القائمة *</Label>
              <Input
                value={newSelector.label || ""}
                onChange={(e) => {
                  const label = e.target.value
                  setNewSelector(prev => ({ 
                    ...prev, 
                    label,
                    name: prev.name || generateInternalName(label)
                  }))
                }}
                className="bg-slate-700/50 border-slate-600 text-white text-sm"
                placeholder="مثال: اختر الباقة"
              />
            </div>
            <div>
              <Label className="text-slate-300 text-sm">الاسم الداخلي</Label>
              <Input
                value={newSelector.name || ""}
                onChange={(e) => setNewSelector(prev => ({ ...prev, name: e.target.value }))}
                className="bg-slate-700/50 border-slate-600 text-white text-sm"
                placeholder="package_type"
              />
            </div>
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <input
                type="checkbox"
                id="new-selector-required"
                checked={newSelector.required || false}
                onChange={(e) => setNewSelector(prev => ({ ...prev, required: e.target.checked }))}
                className="w-4 h-4"
              />
              <Label htmlFor="new-selector-required" className="text-slate-300 text-sm">
                اختيار مطلوب
              </Label>
            </div>
            
            <Button
              onClick={addSelector}
              disabled={!newSelector.label?.trim()}
              className="bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white disabled:opacity-50"
            >
              <Plus className="h-4 w-4 mr-2" />
              إضافة القائمة
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

// ## Option Adder Component
interface OptionAdderProps {
  onAdd: (option: Omit<SelectorOption, 'id'>) => void
}

function OptionAdder({ onAdd }: OptionAdderProps) {
  const [newOption, setNewOption] = useState({
    label: "",
    price: 0,
    value: ""
  })

  const handleAdd = () => {
    if (!newOption.label.trim()) return

    onAdd({
      label: newOption.label.trim(),
      price: newOption.price,
      value: newOption.value.trim() || newOption.label.toLowerCase().replace(/\s+/g, '_')
    })

    setNewOption({ label: "", price: 0, value: "" })
  }

  return (
    <div className="bg-slate-900/30 rounded-lg p-3 border border-dashed border-slate-600">
      <div className="grid grid-cols-1 sm:grid-cols-3 gap-2">
        <Input
          value={newOption.label}
          onChange={(e) => setNewOption(prev => ({ ...prev, label: e.target.value }))}
          className="bg-slate-800/50 border-slate-600 text-white text-sm"
          placeholder="تسمية الخيار"
        />
        <Input
          type="number"
          value={newOption.price}
          onChange={(e) => setNewOption(prev => ({ ...prev, price: Number(e.target.value) }))}
          className="bg-slate-800/50 border-slate-600 text-white text-sm"
          placeholder="السعر"
        />
        <Button
          onClick={handleAdd}
          disabled={!newOption.label.trim()}
          className="bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white disabled:opacity-50"
          size="sm"
        >
          <Plus className="h-3 w-3 mr-1" />
          إضافة
        </Button>
      </div>
    </div>
  )
}
