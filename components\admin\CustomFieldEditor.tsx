"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { 
  Plus, 
  Trash2, 
  GripVertical, 
  Type, 
  Mail, 
  Hash, 
  FileText,
  Edit3,
  Check,
  X
} from "lucide-react"

// ## Interface for Custom Fields - will be used with Supabase
export interface ProductField {
  id: string
  label: string
  type: "text" | "email" | "number" | "textarea"
  name: string
  placeholder?: string
  required: boolean
}

interface CustomFieldEditorProps {
  fields: ProductField[]
  onChange: (fields: ProductField[]) => void
  className?: string
}

export function CustomFieldEditor({ fields, onChange, className = "" }: CustomFieldEditorProps) {
  const [editing<PERSON><PERSON>, set<PERSON><PERSON>ing<PERSON>ield] = useState<string | null>(null)
  const [new<PERSON>ield, setNew<PERSON>ield] = useState<Partial<ProductField>>({
    label: "",
    type: "text",
    name: "",
    placeholder: "",
    required: false
  })

  // ## Field type options with icons
  const fieldTypes = [
    { value: "text", label: "نص عادي", icon: <Type className="h-4 w-4" /> },
    { value: "email", label: "بريد إلكتروني", icon: <Mail className="h-4 w-4" /> },
    { value: "number", label: "رقم", icon: <Hash className="h-4 w-4" /> },
    { value: "textarea", label: "نص طويل", icon: <FileText className="h-4 w-4" /> }
  ]

  // ## Generate internal name from label
  const generateInternalName = (label: string): string => {
    return label
      .toLowerCase()
      .replace(/[^a-zA-Z0-9\u0600-\u06FF]/g, '_')
      .replace(/_+/g, '_')
      .replace(/^_|_$/g, '')
  }

  // ## Add new field
  const addField = () => {
    if (!newField.label?.trim()) return

    const field: ProductField = {
      id: Date.now().toString(),
      label: newField.label.trim(),
      type: newField.type as ProductField['type'],
      name: newField.name?.trim() || generateInternalName(newField.label.trim()),
      placeholder: newField.placeholder?.trim() || "",
      required: newField.required || false
    }

    // ## TODO: Add field to Supabase database
    onChange([...fields, field])
    
    // Reset form
    setNewField({
      label: "",
      type: "text",
      name: "",
      placeholder: "",
      required: false
    })
  }

  // ## Update existing field
  const updateField = (id: string, updates: Partial<ProductField>) => {
    const updatedFields = fields.map(field => 
      field.id === id ? { ...field, ...updates } : field
    )
    
    // ## TODO: Update field in Supabase database
    onChange(updatedFields)
    setEditingField(null)
  }

  // ## Remove field
  const removeField = (id: string) => {
    const updatedFields = fields.filter(field => field.id !== id)
    
    // ## TODO: Remove field from Supabase database
    onChange(updatedFields)
  }

  // ## Move field up/down (simple reordering)
  const moveField = (id: string, direction: 'up' | 'down') => {
    const currentIndex = fields.findIndex(field => field.id === id)
    if (currentIndex === -1) return

    const newIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1
    if (newIndex < 0 || newIndex >= fields.length) return

    const newFields = [...fields]
    const [movedField] = newFields.splice(currentIndex, 1)
    newFields.splice(newIndex, 0, movedField)

    // ## TODO: Update field order in Supabase database
    onChange(newFields)
  }

  return (
    <div className={`space-y-2 sm:space-y-4 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <h3 className="text-base sm:text-lg font-semibold text-white flex items-center gap-2">
          <Edit3 className="h-4 w-4 sm:h-5 sm:w-5 text-blue-400" />
          الحقول المخصصة ({fields.length})
        </h3>
      </div>

      {/* Existing Fields */}
      {fields.length > 0 && (
        <div className="space-y-2 sm:space-y-3">
          {fields.map((field, index) => (
            <Card key={field.id} className="bg-slate-700/30 border-slate-600/50">
              <CardContent className="p-2 sm:p-4">
                {editingField === field.id ? (
                  // Edit Mode
                  <div className="space-y-3">
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                      <div>
                        <Label className="text-slate-300 text-sm">التسمية</Label>
                        <Input
                          value={field.label}
                          onChange={(e) => updateField(field.id, { label: e.target.value })}
                          className="bg-slate-800/50 border-slate-600 text-white text-sm"
                          placeholder="مثال: أدخل بريدك الإلكتروني"
                        />
                      </div>
                      <div>
                        <Label className="text-slate-300 text-sm">النوع</Label>
                        <Select 
                          value={field.type} 
                          onValueChange={(value) => updateField(field.id, { type: value as ProductField['type'] })}
                        >
                          <SelectTrigger className="bg-slate-800/50 border-slate-600 text-white text-sm">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent className="bg-slate-800 border-slate-700">
                            {fieldTypes.map((type) => (
                              <SelectItem key={type.value} value={type.value} className="text-white hover:bg-slate-700">
                                <div className="flex items-center gap-2">
                                  {type.icon}
                                  {type.label}
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                      <div>
                        <Label className="text-slate-300 text-sm">الاسم الداخلي</Label>
                        <Input
                          value={field.name}
                          onChange={(e) => updateField(field.id, { name: e.target.value })}
                          className="bg-slate-800/50 border-slate-600 text-white text-sm"
                          placeholder="email_address"
                        />
                      </div>
                      <div>
                        <Label className="text-slate-300 text-sm">النص التوضيحي</Label>
                        <Input
                          value={field.placeholder || ""}
                          onChange={(e) => updateField(field.id, { placeholder: e.target.value })}
                          className="bg-slate-800/50 border-slate-600 text-white text-sm"
                          placeholder="مثال: <EMAIL>"
                        />
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <input
                          type="checkbox"
                          id={`required-${field.id}`}
                          checked={field.required}
                          onChange={(e) => updateField(field.id, { required: e.target.checked })}
                          className="w-4 h-4"
                        />
                        <Label htmlFor={`required-${field.id}`} className="text-slate-300 text-sm">
                          حقل مطلوب
                        </Label>
                      </div>
                      
                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          onClick={() => setEditingField(null)}
                          className="bg-green-600 hover:bg-green-700 text-white"
                        >
                          <Check className="h-3 w-3" />
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => setEditingField(null)}
                          className="border-slate-600 text-slate-300"
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ) : (
                  // View Mode
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="flex flex-col gap-1">
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => moveField(field.id, 'up')}
                          disabled={index === 0}
                          className="h-4 w-6 p-0 text-slate-400 hover:text-white disabled:opacity-30"
                        >
                          ▲
                        </Button>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => moveField(field.id, 'down')}
                          disabled={index === fields.length - 1}
                          className="h-4 w-6 p-0 text-slate-400 hover:text-white disabled:opacity-30"
                        >
                          ▼
                        </Button>
                      </div>
                      
                      <GripVertical className="h-4 w-4 text-slate-400" />
                      
                      <div>
                        <div className="flex items-center gap-2">
                          {fieldTypes.find(t => t.value === field.type)?.icon}
                          <span className="text-white font-medium">{field.label}</span>
                          {field.required && (
                            <Badge variant="destructive" className="text-xs">مطلوب</Badge>
                          )}
                        </div>
                        <div className="text-slate-400 text-sm">
                          {field.name} • {fieldTypes.find(t => t.value === field.type)?.label}
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => setEditingField(field.id)}
                        className="border-slate-600 text-slate-300 hover:border-yellow-400 hover:text-yellow-400"
                      >
                        <Edit3 className="h-3 w-3" />
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => removeField(field.id)}
                        className="border-slate-600 text-slate-300 hover:border-red-400 hover:text-red-400"
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Add New Field */}
      <Card className="bg-slate-800/30 border-slate-600/50 border-dashed">
        <CardHeader className="p-2 sm:p-6 pb-2 sm:pb-3">
          <CardTitle className="text-white text-sm sm:text-base flex items-center gap-2">
            <Plus className="h-3 w-3 sm:h-4 sm:w-4 text-green-400" />
            إضافة حقل جديد
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-2 sm:space-y-4 p-2 sm:p-6 pt-0">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
            <div>
              <Label className="text-slate-300 text-sm">التسمية *</Label>
              <Input
                value={newField.label || ""}
                onChange={(e) => {
                  const label = e.target.value
                  setNewField(prev => ({ 
                    ...prev, 
                    label,
                    name: prev.name || generateInternalName(label)
                  }))
                }}
                className="bg-slate-700/50 border-slate-600 text-white text-sm"
                placeholder="مثال: أدخل بريدك الإلكتروني"
              />
            </div>
            <div>
              <Label className="text-slate-300 text-sm">النوع</Label>
              <Select 
                value={newField.type || "text"} 
                onValueChange={(value) => setNewField(prev => ({ ...prev, type: value as ProductField['type'] }))}
              >
                <SelectTrigger className="bg-slate-700/50 border-slate-600 text-white text-sm">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-slate-800 border-slate-700">
                  {fieldTypes.map((type) => (
                    <SelectItem key={type.value} value={type.value} className="text-white hover:bg-slate-700">
                      <div className="flex items-center gap-2">
                        {type.icon}
                        {type.label}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
            <div>
              <Label className="text-slate-300 text-sm">الاسم الداخلي</Label>
              <Input
                value={newField.name || ""}
                onChange={(e) => setNewField(prev => ({ ...prev, name: e.target.value }))}
                className="bg-slate-700/50 border-slate-600 text-white text-sm"
                placeholder="email_address"
              />
            </div>
            <div>
              <Label className="text-slate-300 text-sm">النص التوضيحي</Label>
              <Input
                value={newField.placeholder || ""}
                onChange={(e) => setNewField(prev => ({ ...prev, placeholder: e.target.value }))}
                className="bg-slate-700/50 border-slate-600 text-white text-sm"
                placeholder="مثال: <EMAIL>"
              />
            </div>
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <input
                type="checkbox"
                id="new-field-required"
                checked={newField.required || false}
                onChange={(e) => setNewField(prev => ({ ...prev, required: e.target.checked }))}
                className="w-4 h-4"
              />
              <Label htmlFor="new-field-required" className="text-slate-300 text-sm">
                حقل مطلوب
              </Label>
            </div>
            
            <Button
              onClick={addField}
              disabled={!newField.label?.trim()}
              className="bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white disabled:opacity-50"
            >
              <Plus className="h-4 w-4 mr-2" />
              إضافة الحقل
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
