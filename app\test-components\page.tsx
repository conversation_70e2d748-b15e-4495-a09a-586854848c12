"use client"

import { useState } from "react"
import { GoPlayProductComponent } from "@/components/products/GoPlayProductComponent"
import { AppHeader } from "@/components/layout/AppHeader"
import { SideMenu } from "@/components/layout/SideMenu"
import { MobileNavigation } from "@/components/layout/MobileNavigation"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  Gamepad2, 
  CreditCard, 
  Smartphone, 
  TestTube,
  CheckCircle,
  AlertCircle
} from "lucide-react"

// Test product data
const testProducts = {
  freefire: {
    id: "freefire-diamonds",
    name: "شحن جواهر فري فاير",
    image: "/games/freefire.jpg",
    description: "احصل على جواهر فري فاير بأسرع وقت وأفضل الأسعار",
    note: "تأكد من كتابة معرف اللاعب بشكل صحيح",
    basePrice: 0,
    category: "شحن الألعاب",
    inStock: true,
    estimatedTime: "5-15 دقيقة",
    features: [
      "شحن فوري وآمن",
      "دعم فني 24/7",
      "أفضل الأسعار",
      "ضمان الاسترداد"
    ],
    fields: [
      {
        label: "معرف اللاعب",
        type: "text" as const,
        name: "playerId",
        placeholder: "أدخل معرف اللاعب",
        required: true
      },
      {
        label: "البريد الإلكتروني",
        type: "email" as const,
        name: "email",
        placeholder: "<EMAIL>",
        required: true
      }
    ],
    selectors: [
      {
        label: "كمية الجواهر",
        name: "diamonds",
        required: true,
        options: [
          { label: "100 جوهرة", value: "100", price: 10 },
          { label: "310 جوهرة", value: "310", price: 30 },
          { label: "520 جوهرة", value: "520", price: 50 },
          { label: "1080 جوهرة", value: "1080", price: 100 },
          { label: "2200 جوهرة", value: "2200", price: 200 }
        ]
      }
    ]
  },
  steam: {
    id: "steam-wallet",
    name: "بطاقة ستيم",
    image: "/games/steam.jpg",
    description: "بطاقات ستيم الرقمية لشراء الألعاب والمحتوى",
    basePrice: 0,
    category: "بطاقات الدفع",
    inStock: true,
    estimatedTime: "فوري",
    features: [
      "تفعيل فوري",
      "صالحة عالمياً",
      "لا تنتهي الصلاحية"
    ],
    fields: [
      {
        label: "البريد الإلكتروني",
        type: "email" as const,
        name: "email",
        placeholder: "<EMAIL>",
        required: true
      },
      {
        label: "ملاحظات إضافية",
        type: "textarea" as const,
        name: "notes",
        placeholder: "أي ملاحظات خاصة (اختياري)",
        required: false
      }
    ],
    selectors: [
      {
        label: "قيمة البطاقة",
        name: "amount",
        required: true,
        options: [
          { label: "5 دولار", value: "5", price: 25 },
          { label: "10 دولار", value: "10", price: 50 },
          { label: "20 دولار", value: "20", price: 100 },
          { label: "50 دولار", value: "50", price: 250 },
          { label: "100 دولار", value: "100", price: 500 }
        ]
      }
    ]
  }
}

export default function TestComponentsPage() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [activeTab, setActiveTab] = useState("home")
  const [selectedProduct, setSelectedProduct] = useState<keyof typeof testProducts>("freefire")
  const [testResults, setTestResults] = useState<string[]>([])

  const handleTabChange = (tab: string) => {
    setActiveTab(tab)
  }

  const handleTestSubmit = async (data: any) => {
    console.log("Test submission:", data)
    const result = `✅ نجح اختبار ${testProducts[selectedProduct].name} - البيانات: ${JSON.stringify(data, null, 2)}`
    setTestResults(prev => [result, ...prev.slice(0, 4)]) // Keep last 5 results
    
    // Simulate processing
    await new Promise(resolve => setTimeout(resolve, 1000))
    alert("تم اختبار المكون بنجاح! ✅")
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
      <AppHeader onMenuOpen={() => setIsMenuOpen(true)} />
      <SideMenu isOpen={isMenuOpen} onClose={() => setIsMenuOpen(false)} />

      {/* Main Content */}
      <main className="relative z-10 p-4 lg:p-8 space-y-6 pb-32 pt-32 lg:pt-36 max-w-6xl mx-auto">
        
        {/* Page Header */}
        <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="text-white flex items-center gap-3 text-xl sm:text-2xl">
              <TestTube className="h-6 w-6 text-yellow-400" />
              اختبار المكونات الديناميكية
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-slate-300 text-sm sm:text-base mb-4">
              هذه الصفحة لاختبار المكونات الديناميكية والتأكد من عملها على الهاتف المحمول
            </p>
            
            {/* Product Selector */}
            <div className="flex flex-wrap gap-2 mb-4">
              {Object.entries(testProducts).map(([key, product]) => (
                <Button
                  key={key}
                  variant={selectedProduct === key ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedProduct(key as keyof typeof testProducts)}
                  className={selectedProduct === key 
                    ? "bg-gradient-to-r from-yellow-400 to-orange-500 text-slate-900" 
                    : "border-slate-600 text-slate-300 hover:bg-slate-700"
                  }
                >
                  {product.name}
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Component Test */}
        <div className="space-y-4">
          <h2 className="text-xl font-bold text-white text-center">
            اختبار المكون: {testProducts[selectedProduct].name}
          </h2>
          
          <GoPlayProductComponent
            product={testProducts[selectedProduct]}
            onSubmit={handleTestSubmit}
            className="max-w-4xl mx-auto"
          />
        </div>

        {/* Test Results */}
        {testResults.length > 0 && (
          <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-green-400" />
                نتائج الاختبار
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {testResults.map((result, index) => (
                  <div key={index} className="bg-slate-900/50 rounded-lg p-3">
                    <pre className="text-xs text-slate-300 whitespace-pre-wrap break-words">
                      {result}
                    </pre>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Mobile Test Info */}
        <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm lg:hidden">
          <CardHeader>
            <CardTitle className="text-white flex items-center gap-2 text-lg">
              <Smartphone className="h-5 w-5 text-blue-400" />
              اختبار الهاتف المحمول
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm text-slate-300">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-400" />
                <span>تم تحسين التصميم للهاتف المحمول</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-400" />
                <span>أزرار بحجم مناسب للمس</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-400" />
                <span>نصوص قابلة للقراءة</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-400" />
                <span>تخطيط متجاوب</span>
              </div>
            </div>
          </CardContent>
        </Card>

      </main>

      <MobileNavigation activeTab={activeTab} onTabChange={handleTabChange} />
    </div>
  )
}
