"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, Card<PERSON>itle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { 
  Settings, 
  CheckCircle, 
  AlertTriangle,
  Package,
  FolderOpen,
  Plus,
  Edit,
  Eye,
  ArrowRight
} from "lucide-react"

export default function AdminTestPage() {
  const [testResults, setTestResults] = useState<string[]>([])

  const addTestResult = (result: string) => {
    const timestamp = new Date().toLocaleTimeString('ar-SA')
    setTestResults(prev => [`${timestamp} - ${result}`, ...prev.slice(0, 9)])
  }

  const testButtons = [
    {
      title: "اختبار إضافة منتج جديد",
      description: "اختبار فتح نافذة إضافة منتج جديد",
      action: () => {
        // Simulate clicking the add product button
        addTestResult("✅ تم اختبار زر إضافة منتج جديد - يجب أن تفتح نافذة الإضافة")
        window.open('/admin', '_blank')
      },
      icon: <Plus className="h-5 w-5" />,
      color: "bg-green-500/20 text-green-400 border-green-500/30"
    },
    {
      title: "اختبار تعديل منتج",
      description: "اختبار فتح نافذة تعديل منتج موجود",
      action: () => {
        addTestResult("✅ تم اختبار زر تعديل منتج - يجب أن تفتح نافذة التعديل")
        window.open('/admin', '_blank')
      },
      icon: <Edit className="h-5 w-5" />,
      color: "bg-yellow-500/20 text-yellow-400 border-yellow-500/30"
    },
    {
      title: "اختبار عرض تفاصيل منتج",
      description: "اختبار فتح نافذة عرض تفاصيل المنتج",
      action: () => {
        addTestResult("✅ تم اختبار زر عرض المنتج - يجب أن تفتح نافذة التفاصيل")
        window.open('/admin', '_blank')
      },
      icon: <Eye className="h-5 w-5" />,
      color: "bg-blue-500/20 text-blue-400 border-blue-500/30"
    },
    {
      title: "اختبار إدارة الفئات",
      description: "اختبار صفحة إدارة الفئات",
      action: () => {
        addTestResult("✅ تم اختبار صفحة إدارة الفئات")
        window.open('/admin/categories', '_blank')
      },
      icon: <FolderOpen className="h-5 w-5" />,
      color: "bg-purple-500/20 text-purple-400 border-purple-500/30"
    }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white p-6">
      <div className="max-w-4xl mx-auto space-y-6">
        
        {/* Header */}
        <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="text-white flex items-center gap-3 text-2xl">
              <Settings className="h-6 w-6 text-yellow-400" />
              اختبار لوحة الإدارة
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-slate-300 mb-4">
              هذه الصفحة لاختبار وظائف لوحة الإدارة والتأكد من عمل جميع الأزرار والنوافذ
            </p>
            
            <Alert className="bg-blue-500/10 border-blue-500/20">
              <AlertTriangle className="h-4 w-4 text-blue-400" />
              <AlertDescription className="text-blue-200">
                <strong>تعليمات الاختبار:</strong>
                <br />
                1. اضغط على أزرار الاختبار أدناه
                <br />
                2. ستفتح صفحة الإدارة في تبويب جديد
                <br />
                3. اختبر الوظيفة المطلوبة في الصفحة الجديدة
                <br />
                4. تأكد من فتح النوافذ والحفظ بشكل صحيح
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>

        {/* Test Buttons */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {testButtons.map((test, index) => (
            <Card key={index} className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm hover:bg-slate-800/70 transition-colors">
              <CardContent className="p-6">
                <div className="flex items-start gap-4">
                  <div className={`p-3 rounded-lg ${test.color}`}>
                    {test.icon}
                  </div>
                  <div className="flex-1">
                    <h3 className="text-white font-semibold mb-2">{test.title}</h3>
                    <p className="text-slate-300 text-sm mb-4">{test.description}</p>
                    <Button
                      onClick={test.action}
                      className="bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 text-slate-900 font-bold"
                      size="sm"
                    >
                      اختبار الآن
                      <ArrowRight className="h-4 w-4 mr-2" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Quick Access Links */}
        <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="text-white flex items-center gap-2">
              <Package className="h-5 w-5 text-green-400" />
              روابط سريعة للإدارة
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
              <Button
                onClick={() => window.open('/admin', '_blank')}
                variant="outline"
                className="border-slate-600 text-slate-300 hover:border-yellow-400 hover:text-yellow-400 justify-start"
              >
                <Package className="h-4 w-4 mr-2" />
                إدارة المنتجات
              </Button>
              
              <Button
                onClick={() => window.open('/admin/categories', '_blank')}
                variant="outline"
                className="border-slate-600 text-slate-300 hover:border-purple-400 hover:text-purple-400 justify-start"
              >
                <FolderOpen className="h-4 w-4 mr-2" />
                إدارة الفئات
              </Button>
              
              <Button
                onClick={() => window.open('/demo', '_blank')}
                variant="outline"
                className="border-slate-600 text-slate-300 hover:border-blue-400 hover:text-blue-400 justify-start"
              >
                <Eye className="h-4 w-4 mr-2" />
                معاينة المنتجات
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Test Results */}
        {testResults.length > 0 && (
          <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-green-400" />
                سجل الاختبارات ({testResults.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 max-h-60 overflow-y-auto">
                {testResults.map((result, index) => (
                  <div key={index} className="bg-slate-900/50 rounded-lg p-3">
                    <div className="text-sm text-slate-300 break-words">
                      {result}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Status Check */}
        <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="text-white flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-400" />
              حالة المكونات
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-400" />
                <span className="text-sm text-slate-300">ProductDashboard - جاهز</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-400" />
                <span className="text-sm text-slate-300">CategoryDashboard - جاهز</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-400" />
                <span className="text-sm text-slate-300">نوافذ الإضافة والتعديل - مُحدثة</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-400" />
                <span className="text-sm text-slate-300">التصميم المتجاوب - محسن</span>
              </div>
            </div>
          </CardContent>
        </Card>

      </div>
    </div>
  )
}
