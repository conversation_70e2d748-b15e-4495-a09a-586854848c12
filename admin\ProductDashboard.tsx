"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import {
  Plus,
  Edit,
  Trash2,
  Save,
  X,
  AlertCircle,
  CheckCircle,
  Package,
  Eye,
  Settings,
  DollarSign,
  Sparkles
} from "lucide-react"

// Import the new dynamic form system
import { ProductForm, DynamicProduct } from "@/components/admin/ProductForm"

// ## Product Data Structures for Dynamic CMS
interface ProductField {
  id: string
  label: string
  type: "text" | "email" | "textarea" | "number"
  name: string
  placeholder?: string
  required: boolean
}

interface SelectorOption {
  id: string
  label: string
  price: number
  value: string
}

interface ProductSelector {
  id: string
  label: string
  name: string
  options: SelectorOption[]
  required: boolean
}

interface Product {
  id: string
  name: string
  description?: string
  note?: string
  image?: string
  categoryId: string
  basePrice: number
  fields: ProductField[]
  selectors: ProductSelector[]
  isActive: boolean
  inStock: boolean
  estimatedTime?: string
  features?: string[]
  createdAt: string
  updatedAt: string
}

// ## Mock Categories Data
const mockCategories = [
  { id: "1", name: "بطاقات الدفع" },
  { id: "2", name: "شحن الألعاب" },
  { id: "3", name: "خدمات السوشيال" },
  { id: "4", name: "حسابات للبيع" }
]

// ## Mock Products Data - will be replaced with Supabase
const initialProducts: Product[] = [
  {
    id: "1",
    name: "شحن فري فاير",
    description: "شحن فوري للجواهر في لعبة Free Fire",
    note: "الرجاء إدخال ID واختيار الباقة المناسبة",
    image: "/images/freefire.png",
    categoryId: "2",
    basePrice: 0,
    fields: [
      {
        id: "1",
        label: "أدخل ID فري فاير",
        type: "text",
        name: "freefire_id",
        placeholder: "مثال: 123456789",
        required: true
      }
    ],
    selectors: [
      {
        id: "1",
        label: "اختر الباقة",
        name: "package",
        required: true,
        options: [
          { id: "1", label: "110 جوهرة", price: 2000, value: "110_diamonds" },
          { id: "2", label: "عضوية أسبوعية", price: 3000, value: "weekly_membership" }
        ]
      }
    ],
    isActive: true,
    inStock: true,
    estimatedTime: "أقل من دقيقة",
    features: ["شحن فوري", "دعم جميع السيرفرات"],
    createdAt: "2024-01-01T00:00:00Z",
    updatedAt: "2024-01-01T00:00:00Z"
  },
  {
    id: "2",
    name: "بطاقة Steam",
    description: "بطاقات شحن Steam للألعاب",
    categoryId: "1",
    basePrice: 0,
    fields: [
      {
        id: "1",
        label: "البريد الإلكتروني",
        type: "email",
        name: "email",
        placeholder: "<EMAIL>",
        required: true
      }
    ],
    selectors: [
      {
        id: "1",
        label: "اختر القيمة",
        name: "amount",
        required: true,
        options: [
          { id: "1", label: "250 ج.س", price: 250, value: "250" },
          { id: "2", label: "500 ج.س", price: 500, value: "500" },
          { id: "3", label: "1000 ج.س", price: 1000, value: "1000" }
        ]
      }
    ],
    isActive: true,
    inStock: true,
    estimatedTime: "فوري",
    features: ["تفعيل فوري", "صالحة عالمياً"],
    createdAt: "2024-01-01T00:00:00Z",
    updatedAt: "2024-01-01T00:00:00Z"
  }
]

export function ProductDashboard() {
  const [products, setProducts] = useState<Product[]>(initialProducts)
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [editingProduct, setEditingProduct] = useState<Product | null>(null)
  const [viewingProduct, setViewingProduct] = useState<Product | null>(null)

  // ## Convert legacy Product to DynamicProduct format
  const convertToLegacyProduct = (product: Product): DynamicProduct => {
    return {
      id: product.id,
      name: product.name,
      image: product.image || "",
      description: product.description || "",
      note: product.note || "",
      estimatedTime: product.estimatedTime || "",
      price: product.basePrice,
      category: product.categoryId,
      active: product.isActive,
      inStock: product.inStock,
      fields: product.fields || [],
      selectors: product.selectors || []
    }
  }

  // ## Convert DynamicProduct back to legacy Product format
  const convertFromDynamicProduct = (dynamicProduct: DynamicProduct): Product => {
    return {
      id: dynamicProduct.id || Date.now().toString(),
      name: dynamicProduct.name,
      description: dynamicProduct.description,
      note: dynamicProduct.note,
      image: dynamicProduct.image,
      categoryId: dynamicProduct.category,
      basePrice: dynamicProduct.price,
      estimatedTime: dynamicProduct.estimatedTime,
      isActive: dynamicProduct.active,
      inStock: dynamicProduct.inStock,
      fields: dynamicProduct.fields,
      selectors: dynamicProduct.selectors,
      features: [], // Legacy field
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  }

  // ## Handle saving product from dynamic form
  const handleSaveProduct = (dynamicProduct: DynamicProduct) => {
    const product = convertFromDynamicProduct(dynamicProduct)

    if (editingProduct) {
      // ## TODO: Update product in Supabase database
      setProducts(prev => prev.map(p => p.id === editingProduct.id ? product : p))
    } else {
      // ## TODO: Create product in Supabase database
      setProducts(prev => [...prev, product])
    }

    closeDialog()
  }



  // Open create dialog
  const openCreateDialog = () => {
    setEditingProduct(null)
    setIsCreateDialogOpen(true)
  }

  // Open edit dialog
  const openEditDialog = (product: Product) => {
    setEditingProduct(product)
    setIsCreateDialogOpen(true)
  }

  // Close dialog
  const closeDialog = () => {
    setIsCreateDialogOpen(false)
    setEditingProduct(null)
    setViewingProduct(null)
  }

  // Get category name
  const getCategoryName = (categoryId: string) => {
    return mockCategories.find(cat => cat.id === categoryId)?.name || "غير محدد"
  }

  // Calculate total price for display
  const calculateDisplayPrice = (product: Product) => {
    const minSelectorPrice = Math.min(...product.selectors.flatMap(s => s.options.map(o => o.price)))
    return product.basePrice + (minSelectorPrice || 0)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white p-3 sm:p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6 sm:mb-8">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent">
              إدارة المنتجات
            </h1>
            <p className="text-slate-300 mt-2 text-sm sm:text-base">
              إدارة المنتجات والخدمات المتاحة
            </p>
          </div>

          <Button
            onClick={openCreateDialog}
            className="bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 text-slate-900 font-bold w-full sm:w-auto"
          >
            <Plus className="h-4 w-4 mr-2" />
            إضافة منتج جديد
          </Button>
        </div>

        {/* Products Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
          {products.map((product) => (
            <Card key={product.id} className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
              <CardHeader className="pb-3">
                <div className="flex justify-between items-start">
                  <div className="flex items-center gap-3">
                    <div className="w-12 h-12 rounded-lg overflow-hidden bg-slate-700/50 flex items-center justify-center">
                      {product.image ? (
                        <img src={product.image} alt={product.name} className="w-full h-full object-cover" />
                      ) : (
                        <Package className="h-6 w-6 text-slate-400" />
                      )}
                    </div>
                    <div>
                      <CardTitle className="text-white text-lg">{product.name}</CardTitle>
                      <p className="text-slate-400 text-sm">{getCategoryName(product.categoryId)}</p>
                    </div>
                  </div>
                </div>
              </CardHeader>
              
              <CardContent className="space-y-4">
                <div className="flex flex-wrap gap-2">
                  <Badge 
                    variant={product.isActive ? "default" : "secondary"}
                    className={product.isActive 
                      ? "bg-green-500/20 text-green-400 border-green-500/30" 
                      : "bg-slate-600/20 text-slate-400 border-slate-600/30"
                    }
                  >
                    {product.isActive ? "نشط" : "غير نشط"}
                  </Badge>
                  
                  <Badge 
                    variant={product.inStock ? "default" : "secondary"}
                    className={product.inStock 
                      ? "bg-blue-500/20 text-blue-400 border-blue-500/30" 
                      : "bg-red-500/20 text-red-400 border-red-500/30"
                    }
                  >
                    {product.inStock ? "متوفر" : "غير متوفر"}
                  </Badge>
                </div>
                
                {product.description && (
                  <p className="text-slate-300 text-sm line-clamp-2">{product.description}</p>
                )}
                
                <div className="flex items-center gap-2 text-yellow-400">
                  <DollarSign className="h-4 w-4" />
                  <span className="font-medium">من {calculateDisplayPrice(product)} ج.س</span>
                </div>
                
                <div className="text-xs text-slate-400">
                  <div>الحقول: {product.fields.length}</div>
                  <div>الخيارات: {product.selectors.length}</div>
                </div>
                
                <div className="flex flex-col sm:flex-row gap-2 pt-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setViewingProduct(product)}
                    className="border-slate-600 text-slate-300 hover:border-blue-400 hover:text-blue-400 flex-1 sm:flex-none"
                  >
                    <Eye className="h-3 w-3 mr-1" />
                    عرض
                  </Button>

                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => openEditDialog(product)}
                    className="border-slate-600 text-slate-300 hover:border-yellow-400 hover:text-yellow-400 flex-1 sm:flex-none"
                  >
                    <Edit className="h-3 w-3 mr-1" />
                    تعديل
                  </Button>

                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => {
                      if (confirm("هل أنت متأكد من حذف هذا المنتج؟")) {
                        setProducts(prev => prev.filter(p => p.id !== product.id))
                      }
                    }}
                    className="border-slate-600 text-slate-300 hover:border-red-400 hover:text-red-400 flex-1 sm:flex-none"
                  >
                    <Trash2 className="h-3 w-3 mr-1" />
                    حذف
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Create/Edit Product Dialog - Now using Dynamic Form System */}
        <Dialog open={isCreateDialogOpen} onOpenChange={closeDialog}>
          <DialogContent className="bg-slate-900 border-slate-700 text-white w-[96vw] max-w-none sm:max-w-[90vw] lg:max-w-6xl h-[92vh] max-h-none overflow-hidden p-0 fixed inset-2 sm:inset-4">
            <DialogHeader className="sr-only">
              <DialogTitle>
                {editingProduct ? "تعديل المنتج" : "إضافة منتج جديد"}
              </DialogTitle>
            </DialogHeader>
            <div className="h-full flex flex-col">
              <div className="flex-1 overflow-y-auto">
                <div className="p-3 sm:p-6">
                  <ProductForm
                    product={editingProduct ? convertToLegacyProduct(editingProduct) : undefined}
                    onSave={handleSaveProduct}
                    onCancel={closeDialog}
                    categories={mockCategories}
                    isEditing={!!editingProduct}
                  />
                </div>
              </div>
            </div>
          </DialogContent>
        </Dialog>



        {/* View Product Dialog */}
        <Dialog open={!!viewingProduct} onOpenChange={() => setViewingProduct(null)}>
          <DialogContent className="bg-slate-800 border-slate-700 text-white w-[94vw] max-w-none sm:max-w-2xl h-[88vh] max-h-none overflow-y-auto fixed inset-3 sm:inset-auto sm:relative">
            <DialogHeader>
              <DialogTitle className="text-xl font-bold">
                تفاصيل المنتج: {viewingProduct?.name}
              </DialogTitle>
            </DialogHeader>

            {viewingProduct && (
              <div className="space-y-6">
                {/* Basic Info */}
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label className="text-slate-300">الاسم</Label>
                    <p className="text-white">{viewingProduct.name}</p>
                  </div>
                  <div>
                    <Label className="text-slate-300">الفئة</Label>
                    <p className="text-white">{getCategoryName(viewingProduct.categoryId)}</p>
                  </div>
                  <div>
                    <Label className="text-slate-300">السعر الأساسي</Label>
                    <p className="text-white">{viewingProduct.basePrice} ج.س</p>
                  </div>
                  <div>
                    <Label className="text-slate-300">الوقت المتوقع</Label>
                    <p className="text-white">{viewingProduct.estimatedTime || "غير محدد"}</p>
                  </div>
                </div>

                {viewingProduct.description && (
                  <div>
                    <Label className="text-slate-300">الوصف</Label>
                    <p className="text-white">{viewingProduct.description}</p>
                  </div>
                )}

                {viewingProduct.note && (
                  <div>
                    <Label className="text-slate-300">ملاحظة</Label>
                    <p className="text-yellow-200">{viewingProduct.note}</p>
                  </div>
                )}

                {/* Fields */}
                <div>
                  <Label className="text-slate-300 text-lg">الحقول المطلوبة ({viewingProduct.fields.length})</Label>
                  <div className="space-y-2 mt-2">
                    {viewingProduct.fields.map((field) => (
                      <div key={field.id} className="bg-slate-700/30 p-3 rounded-lg">
                        <div className="flex justify-between items-center">
                          <span className="text-white font-medium">{field.label}</span>
                          <div className="flex gap-2">
                            <Badge variant="outline" className="text-xs">
                              {field.type}
                            </Badge>
                            {field.required && (
                              <Badge variant="destructive" className="text-xs">
                                مطلوب
                              </Badge>
                            )}
                          </div>
                        </div>
                        {field.placeholder && (
                          <p className="text-slate-400 text-sm mt-1">
                            مثال: {field.placeholder}
                          </p>
                        )}
                      </div>
                    ))}
                  </div>
                </div>

                {/* Selectors */}
                <div>
                  <Label className="text-slate-300 text-lg">قوائم الاختيار ({viewingProduct.selectors.length})</Label>
                  <div className="space-y-3 mt-2">
                    {viewingProduct.selectors.map((selector) => (
                      <div key={selector.id} className="bg-slate-700/30 p-3 rounded-lg">
                        <div className="flex justify-between items-center mb-2">
                          <span className="text-white font-medium">{selector.label}</span>
                          {selector.required && (
                            <Badge variant="destructive" className="text-xs">
                              مطلوب
                            </Badge>
                          )}
                        </div>
                        <div className="space-y-1">
                          {selector.options.map((option) => (
                            <div key={option.id} className="flex justify-between items-center text-sm">
                              <span className="text-slate-300">{option.label}</span>
                              <span className="text-yellow-400 font-medium">
                                {option.price > 0 ? `+${option.price}` : option.price} ج.س
                              </span>
                            </div>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Features */}
                {viewingProduct.features && viewingProduct.features.length > 0 && (
                  <div>
                    <Label className="text-slate-300 text-lg">المميزات</Label>
                    <ul className="list-disc list-inside text-slate-300 mt-2 space-y-1">
                      {viewingProduct.features.map((feature, index) => (
                        <li key={index}>{feature}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            )}
          </DialogContent>
        </Dialog>
      </div>
    </div>
  )
}
