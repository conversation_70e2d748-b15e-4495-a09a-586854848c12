"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON>eader, Card<PERSON>itle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Textarea } from "@/components/ui/textarea"
import { 
  Plus, 
  Edit, 
  Trash2, 
  Save, 
  X, 
  AlertCircle,
  CheckCircle,
  Folder,
  FolderOpen
} from "lucide-react"

// ## Category Data Structure
interface Category {
  id: string
  name: string
  description?: string
  icon?: string
  isActive: boolean
  createdAt: string
  updatedAt: string
}

// ## Mock Categories Data - will be replaced with Supabase
const initialCategories: Category[] = [
  {
    id: "1",
    name: "بطاقات الدفع",
    description: "بطاقات الدفع الرقمية والمحافظ الإلكترونية",
    icon: "💳",
    isActive: true,
    createdAt: "2024-01-01T00:00:00Z",
    updatedAt: "2024-01-01T00:00:00Z"
  },
  {
    id: "2", 
    name: "شحن الألعاب",
    description: "شحن العملات والجواهر للألعاب المختلفة",
    icon: "🎮",
    isActive: true,
    createdAt: "2024-01-01T00:00:00Z",
    updatedAt: "2024-01-01T00:00:00Z"
  },
  {
    id: "3",
    name: "خدمات السوشيال",
    description: "خدمات وسائل التواصل الاجتماعي",
    icon: "📱",
    isActive: true,
    createdAt: "2024-01-01T00:00:00Z",
    updatedAt: "2024-01-01T00:00:00Z"
  },
  {
    id: "4",
    name: "حسابات للبيع",
    description: "حسابات الألعاب والتطبيقات المختلفة",
    icon: "👤",
    isActive: false,
    createdAt: "2024-01-01T00:00:00Z",
    updatedAt: "2024-01-01T00:00:00Z"
  }
]

export function CategoryDashboard() {
  const [categories, setCategories] = useState<Category[]>(initialCategories)
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [editingCategory, setEditingCategory] = useState<Category | null>(null)
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    icon: "",
    isActive: true
  })
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Reset form
  const resetForm = () => {
    setFormData({
      name: "",
      description: "",
      icon: "",
      isActive: true
    })
    setErrors({})
    setEditingCategory(null)
  }

  // Open create dialog
  const openCreateDialog = () => {
    resetForm()
    setIsCreateDialogOpen(true)
  }

  // Open edit dialog
  const openEditDialog = (category: Category) => {
    setFormData({
      name: category.name,
      description: category.description || "",
      icon: category.icon || "",
      isActive: category.isActive
    })
    setEditingCategory(category)
    setIsCreateDialogOpen(true)
  }

  // Close dialog
  const closeDialog = () => {
    setIsCreateDialogOpen(false)
    resetForm()
  }

  // Validate form
  const validateForm = () => {
    const newErrors: Record<string, string> = {}
    
    if (!formData.name.trim()) {
      newErrors.name = "اسم الفئة مطلوب"
    }
    
    if (formData.name.trim().length < 2) {
      newErrors.name = "اسم الفئة يجب أن يكون أكثر من حرفين"
    }
    
    // Check for duplicate names (excluding current category when editing)
    const existingCategory = categories.find(cat => 
      cat.name.toLowerCase() === formData.name.toLowerCase() && 
      cat.id !== editingCategory?.id
    )
    
    if (existingCategory) {
      newErrors.name = "اسم الفئة موجود بالفعل"
    }
    
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // Handle form submission
  const handleSubmit = async () => {
    if (!validateForm()) return
    
    setIsSubmitting(true)
    
    try {
      // ## TODO: Replace with Supabase operations
      await new Promise(resolve => setTimeout(resolve, 500)) // Simulate API call
      
      if (editingCategory) {
        // Update existing category
        const updatedCategory: Category = {
          ...editingCategory,
          name: formData.name.trim(),
          description: formData.description.trim(),
          icon: formData.icon.trim(),
          isActive: formData.isActive,
          updatedAt: new Date().toISOString()
        }
        
        setCategories(prev => 
          prev.map(cat => cat.id === editingCategory.id ? updatedCategory : cat)
        )
      } else {
        // Create new category
        const newCategory: Category = {
          id: Date.now().toString(), // In real app, this would be generated by Supabase
          name: formData.name.trim(),
          description: formData.description.trim(),
          icon: formData.icon.trim(),
          isActive: formData.isActive,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
        
        setCategories(prev => [...prev, newCategory])
      }
      
      closeDialog()
    } catch (error) {
      console.error("Error saving category:", error)
      alert("حدث خطأ أثناء حفظ الفئة")
    } finally {
      setIsSubmitting(false)
    }
  }

  // Delete category
  const handleDelete = async (categoryId: string) => {
    if (!confirm("هل أنت متأكد من حذف هذه الفئة؟")) return
    
    try {
      // ## TODO: Replace with Supabase delete operation
      await new Promise(resolve => setTimeout(resolve, 300))
      
      setCategories(prev => prev.filter(cat => cat.id !== categoryId))
    } catch (error) {
      console.error("Error deleting category:", error)
      alert("حدث خطأ أثناء حذف الفئة")
    }
  }

  // Toggle category status
  const toggleCategoryStatus = async (categoryId: string) => {
    try {
      // ## TODO: Replace with Supabase update operation
      await new Promise(resolve => setTimeout(resolve, 200))
      
      setCategories(prev => 
        prev.map(cat => 
          cat.id === categoryId 
            ? { ...cat, isActive: !cat.isActive, updatedAt: new Date().toISOString() }
            : cat
        )
      )
    } catch (error) {
      console.error("Error updating category status:", error)
      alert("حدث خطأ أثناء تحديث حالة الفئة")
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white p-6">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent">
              إدارة الفئات
            </h1>
            <p className="text-slate-300 mt-2">
              إدارة فئات المنتجات والخدمات
            </p>
          </div>
          
          <Button
            onClick={openCreateDialog}
            className="bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 text-slate-900 font-bold"
          >
            <Plus className="h-4 w-4 mr-2" />
            إضافة فئة جديدة
          </Button>
        </div>

        {/* Categories Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {categories.map((category) => (
            <Card key={category.id} className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
              <CardHeader className="pb-3">
                <div className="flex justify-between items-start">
                  <div className="flex items-center gap-3">
                    <div className="text-2xl">
                      {category.icon || (category.isActive ? <FolderOpen /> : <Folder />)}
                    </div>
                    <div>
                      <CardTitle className="text-white text-lg">{category.name}</CardTitle>
                      <Badge 
                        variant={category.isActive ? "default" : "secondary"}
                        className={category.isActive 
                          ? "bg-green-500/20 text-green-400 border-green-500/30" 
                          : "bg-slate-600/20 text-slate-400 border-slate-600/30"
                        }
                      >
                        {category.isActive ? "نشط" : "غير نشط"}
                      </Badge>
                    </div>
                  </div>
                </div>
              </CardHeader>
              
              <CardContent className="space-y-4">
                {category.description && (
                  <p className="text-slate-300 text-sm">{category.description}</p>
                )}
                
                <div className="text-xs text-slate-400">
                  <div>تم الإنشاء: {(() => {
                    const date = new Date(category.createdAt)
                    const year = date.getFullYear()
                    const month = String(date.getMonth() + 1).padStart(2, '0')
                    const day = String(date.getDate()).padStart(2, '0')
                    return `${day}/${month}/${year}`
                  })()}</div>
                  <div>آخر تحديث: {(() => {
                    const date = new Date(category.updatedAt)
                    const year = date.getFullYear()
                    const month = String(date.getMonth() + 1).padStart(2, '0')
                    const day = String(date.getDate()).padStart(2, '0')
                    return `${day}/${month}/${year}`
                  })()}</div>
                </div>
                
                <div className="flex gap-2 pt-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => openEditDialog(category)}
                    className="border-slate-600 text-slate-300 hover:border-yellow-400 hover:text-yellow-400"
                  >
                    <Edit className="h-3 w-3 mr-1" />
                    تعديل
                  </Button>
                  
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => toggleCategoryStatus(category.id)}
                    className={category.isActive 
                      ? "border-slate-600 text-slate-300 hover:border-red-400 hover:text-red-400"
                      : "border-slate-600 text-slate-300 hover:border-green-400 hover:text-green-400"
                    }
                  >
                    {category.isActive ? "إلغاء تفعيل" : "تفعيل"}
                  </Button>
                  
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleDelete(category.id)}
                    className="border-slate-600 text-slate-300 hover:border-red-400 hover:text-red-400"
                  >
                    <Trash2 className="h-3 w-3 mr-1" />
                    حذف
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Create/Edit Dialog */}
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogContent className="bg-slate-800 border-slate-700 text-white max-w-md">
            <DialogHeader>
              <DialogTitle className="text-xl font-bold">
                {editingCategory ? "تعديل الفئة" : "إضافة فئة جديدة"}
              </DialogTitle>
            </DialogHeader>

            <div className="space-y-4">
              {/* Category Name */}
              <div className="space-y-2">
                <Label className="text-white font-medium">
                  اسم الفئة <span className="text-red-400">*</span>
                </Label>
                <Input
                  placeholder="مثال: بطاقات الدفع"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  className="bg-slate-700/50 border-slate-600 text-white placeholder:text-slate-400 focus:border-yellow-400"
                />
                {errors.name && (
                  <p className="text-red-400 text-sm">{errors.name}</p>
                )}
              </div>

              {/* Category Description */}
              <div className="space-y-2">
                <Label className="text-white font-medium">الوصف</Label>
                <Textarea
                  placeholder="وصف مختصر للفئة..."
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  className="bg-slate-700/50 border-slate-600 text-white placeholder:text-slate-400 focus:border-yellow-400"
                  rows={3}
                />
              </div>

              {/* Category Icon */}
              <div className="space-y-2">
                <Label className="text-white font-medium">الأيقونة (اختياري)</Label>
                <Input
                  placeholder="🎮 أو أي رمز تعبيري"
                  value={formData.icon}
                  onChange={(e) => setFormData(prev => ({ ...prev, icon: e.target.value }))}
                  className="bg-slate-700/50 border-slate-600 text-white placeholder:text-slate-400 focus:border-yellow-400"
                />
              </div>

              {/* Category Status */}
              <div className="flex items-center gap-3">
                <input
                  type="checkbox"
                  id="isActive"
                  checked={formData.isActive}
                  onChange={(e) => setFormData(prev => ({ ...prev, isActive: e.target.checked }))}
                  className="w-4 h-4 text-yellow-400 bg-slate-700 border-slate-600 rounded focus:ring-yellow-400"
                />
                <Label htmlFor="isActive" className="text-white font-medium">
                  فئة نشطة
                </Label>
              </div>

              {/* Action Buttons */}
              <div className="flex gap-3 pt-4">
                <Button
                  onClick={handleSubmit}
                  disabled={isSubmitting}
                  className="flex-1 bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 text-slate-900 font-bold"
                >
                  {isSubmitting ? (
                    <div className="flex items-center gap-2">
                      <div className="w-4 h-4 border-2 border-slate-900/30 border-t-slate-900 rounded-full animate-spin" />
                      جاري الحفظ...
                    </div>
                  ) : (
                    <div className="flex items-center gap-2">
                      <Save className="h-4 w-4" />
                      {editingCategory ? "تحديث" : "إضافة"}
                    </div>
                  )}
                </Button>

                <Button
                  onClick={closeDialog}
                  variant="outline"
                  className="border-slate-600 text-slate-300 hover:border-red-400 hover:text-red-400"
                >
                  <X className="h-4 w-4 mr-1" />
                  إلغاء
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  )
}
