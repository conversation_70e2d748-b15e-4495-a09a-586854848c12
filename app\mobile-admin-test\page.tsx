"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Smartphone, Tablet, Monitor, CheckCircle, AlertCircle } from "lucide-react"

export default function MobileAdminTestPage() {
  const [currentView, setCurrentView] = useState<'mobile' | 'tablet' | 'desktop'>('mobile')

  const viewports = {
    mobile: { width: '375px', label: 'Mobile (375px)', icon: <Smartphone className="h-4 w-4" /> },
    tablet: { width: '768px', label: 'Tablet (768px)', icon: <Tablet className="h-4 w-4" /> },
    desktop: { width: '100%', label: 'Desktop (Full)', icon: <Monitor className="h-4 w-4" /> }
  }

  const testCases = [
    {
      id: 'admin-dashboard',
      title: 'Admin Dashboard',
      description: 'Test the main admin dashboard layout and responsiveness',
      url: '/admin-test',
      status: 'fixed'
    },
    {
      id: 'product-form',
      title: 'Dynamic Product Form',
      description: 'Test the create/edit product dialog on mobile devices',
      url: '/admin-test',
      action: 'Click "إضافة منتج جديد" button',
      status: 'fixed'
    },
    {
      id: 'custom-fields',
      title: 'Custom Fields Editor',
      description: 'Test adding and editing custom fields on mobile',
      url: '/admin-test',
      action: 'Open product form → Add custom fields',
      status: 'fixed'
    },
    {
      id: 'select-menus',
      title: 'Select Menus Editor',
      description: 'Test creating select menus with pricing options',
      url: '/admin-test',
      action: 'Open product form → Add select menus',
      status: 'fixed'
    },
    {
      id: 'date-formatting',
      title: 'Date Formatting Consistency',
      description: 'Verify no hydration mismatches with date formatting',
      url: '/admin-test',
      status: 'fixed'
    }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 p-4">
      <div className="max-w-6xl mx-auto space-y-6">
        {/* Header */}
        <Card className="bg-slate-800/50 border-slate-700/50">
          <CardHeader>
            <CardTitle className="text-white flex items-center gap-3">
              <Smartphone className="h-6 w-6 text-blue-400" />
              Mobile Admin Dashboard Testing
              <Badge variant="outline" className="text-green-400 border-green-400">
                All Issues Fixed
              </Badge>
            </CardTitle>
            <p className="text-slate-300">
              Test the mobile responsiveness and functionality of the dynamic CMS admin dashboard
            </p>
          </CardHeader>
        </Card>

        {/* Viewport Selector */}
        <Card className="bg-slate-800/50 border-slate-700/50">
          <CardHeader>
            <CardTitle className="text-white text-lg">Viewport Simulator</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2 mb-4">
              {Object.entries(viewports).map(([key, viewport]) => (
                <Button
                  key={key}
                  variant={currentView === key ? "default" : "outline"}
                  onClick={() => setCurrentView(key as any)}
                  className={`flex items-center gap-2 ${
                    currentView === key 
                      ? "bg-blue-500 text-white" 
                      : "border-slate-600 text-slate-300 hover:border-blue-400"
                  }`}
                >
                  {viewport.icon}
                  {viewport.label}
                </Button>
              ))}
            </div>
            
            <div className="text-sm text-slate-400 mb-4">
              Current viewport: <span className="text-blue-400">{viewports[currentView].width}</span>
            </div>

            {/* Viewport Frame */}
            <div className="border border-slate-600 rounded-lg overflow-hidden bg-white">
              <iframe
                src="/admin-test"
                className="w-full h-[600px] border-0"
                style={{ 
                  width: viewports[currentView].width,
                  maxWidth: '100%',
                  margin: '0 auto',
                  display: 'block'
                }}
                title="Admin Dashboard Mobile Test"
              />
            </div>
          </CardContent>
        </Card>

        {/* Test Cases */}
        <Card className="bg-slate-800/50 border-slate-700/50">
          <CardHeader>
            <CardTitle className="text-white text-lg">Test Cases Status</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {testCases.map((testCase) => (
                <div key={testCase.id} className="flex items-start gap-4 p-4 bg-slate-700/30 rounded-lg">
                  <div className="flex-shrink-0 mt-1">
                    {testCase.status === 'fixed' ? (
                      <CheckCircle className="h-5 w-5 text-green-400" />
                    ) : (
                      <AlertCircle className="h-5 w-5 text-yellow-400" />
                    )}
                  </div>
                  <div className="flex-1">
                    <h4 className="text-white font-medium">{testCase.title}</h4>
                    <p className="text-slate-300 text-sm mb-2">{testCase.description}</p>
                    {testCase.action && (
                      <p className="text-blue-400 text-sm">
                        <strong>Action:</strong> {testCase.action}
                      </p>
                    )}
                  </div>
                  <Badge 
                    variant={testCase.status === 'fixed' ? 'default' : 'secondary'}
                    className={testCase.status === 'fixed' 
                      ? 'bg-green-500/20 text-green-400 border-green-500/30'
                      : 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30'
                    }
                  >
                    {testCase.status === 'fixed' ? 'Fixed' : 'Pending'}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Fixed Issues Summary */}
        <Card className="bg-slate-800/50 border-slate-700/50">
          <CardHeader>
            <CardTitle className="text-white text-lg flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-400" />
              Issues Resolved
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 text-slate-300">
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-green-400 rounded-full mt-2"></div>
                <div>
                  <strong className="text-white">Hydration Mismatch Fixed:</strong> Replaced locale-dependent date formatting with consistent manual formatting to prevent server/client mismatches
                </div>
              </div>
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-green-400 rounded-full mt-2"></div>
                <div>
                  <strong className="text-white">Mobile Dialog Responsiveness:</strong> Updated ProductForm dialog to use responsive max-width (95vw on mobile, 90vw on tablet, 7xl on desktop)
                </div>
              </div>
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-green-400 rounded-full mt-2"></div>
                <div>
                  <strong className="text-white">Mobile Button Layout:</strong> Changed action buttons to stack vertically on mobile with full-width for better touch interaction
                </div>
              </div>
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-green-400 rounded-full mt-2"></div>
                <div>
                  <strong className="text-white">Favicon Issues:</strong> Updated favicon configuration to use existing logo files and removed missing file references
                </div>
              </div>
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-green-400 rounded-full mt-2"></div>
                <div>
                  <strong className="text-white">Grid Responsiveness:</strong> Improved product grid to show 1 column on mobile, 2 on tablet, 3 on desktop
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <Card className="bg-slate-800/50 border-slate-700/50">
          <CardHeader>
            <CardTitle className="text-white text-lg">Quick Test Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-3">
              <Button 
                onClick={() => window.open('/admin-test', '_blank')}
                className="bg-blue-500 hover:bg-blue-600 text-white"
              >
                Open Admin Dashboard
              </Button>
              <Button 
                onClick={() => window.open('/admin-test', '_blank')}
                variant="outline"
                className="border-slate-600 text-slate-300 hover:border-green-400 hover:text-green-400"
              >
                Test Product Creation
              </Button>
              <Button 
                onClick={() => window.open('/', '_blank')}
                variant="outline"
                className="border-slate-600 text-slate-300 hover:border-purple-400 hover:text-purple-400"
              >
                Test Main Site
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
